"""
Setup script for browser-use automation with Gemini
Installs all required dependencies
"""

import subprocess
import sys
import os
from pathlib import Path

def run_command(command, description):
    """Run a command and handle errors"""
    print(f"🔧 {description}...")
    try:
        result = subprocess.run(command, shell=True, check=True, capture_output=True, text=True)
        print(f"✅ {description} completed!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {description} failed!")
        print(f"Error: {e.stderr}")
        return False

def main():
    """Main setup function"""
    print("🚀 Setting up Browser-use with Gemini")
    print("=" * 40)
    
    # Check Python version
    if sys.version_info < (3, 11):
        print("❌ Python 3.11 or higher is required!")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✅ Python version: {sys.version}")
    
    # Install browser-use
    if not run_command("pip install browser-use", "Installing browser-use"):
        return False
    
    # Install playwright and chromium
    if not run_command("pip install playwright", "Installing playwright"):
        return False
    
    if not run_command("playwright install chromium --with-deps", "Installing Chromium browser"):
        return False
    
    # Install additional dependencies
    if not run_command("pip install python-dotenv", "Installing python-dotenv"):
        return False
    
    # Create directories
    print("📁 Creating directories...")
    Path("recordings").mkdir(exist_ok=True)
    Path("logs").mkdir(exist_ok=True)
    print("✅ Directories created!")
    
    # Check if .env file needs API key
    env_file = Path(".env")
    if env_file.exists():
        content = env_file.read_text()
        if "your_gemini_api_key_here" in content:
            print("\n⚠️  IMPORTANT: Update your .env file!")
            print("📝 Add your Gemini API key to the .env file")
            print("🔗 Get your free key at: https://aistudio.google.com/app/u/1/apikey")
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Add your Gemini API key to the .env file")
    print("2. Run: python chrome_automation.py")
    print("3. Watch the automation in action!")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1)
