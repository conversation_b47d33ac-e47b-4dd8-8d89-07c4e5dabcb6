# 🤖 Browser-use Automation with Gemini

Automated browser control using your existing Chrome Profile 2 with Google's Gemini AI.

## 🚀 Quick Start

### 1. Get Your Gemini API Key
1. Visit [Google AI Studio](https://aistudio.google.com/app/u/1/apikey)
2. Sign in with your Google account
3. Click "Create API Key"
4. Copy the generated key

### 2. Setup (Choose One Method)

#### Method A: Automatic Setup (Recommended)
```cmd
python setup.py
```

#### Method B: Manual Setup
```cmd
pip install browser-use playwright python-dotenv
playwright install chromium --with-deps
```

### 3. Configure API Key
1. Open the `.env` file in a text editor
2. Replace `your_gemini_api_key_here` with your actual API key:
   ```
   GEMINI_API_KEY=AIzaSyC-your-actual-api-key-here
   ```

### 4. Run Automation
```cmd
python chrome_automation.py
```

Or double-click: `run_automation.bat`

## 📁 Files Created

- `chrome_automation.py` - Main automation script
- `setup.py` - Dependency installer
- `run_automation.bat` - Windows batch file for easy execution
- `requirements.txt` - Python dependencies
- `.env` - Environment variables (API keys)
- `browser_session_state.json` - Saves your browser session
- `recordings/` - Video recordings of automation sessions

## 🎯 What It Does

The automation script will:
1. ✅ Connect to your existing Chrome Profile 2
2. ✅ Preserve all your login sessions and bookmarks
3. ✅ Use Gemini AI to control the browser
4. ✅ Execute web automation tasks
5. ✅ Keep browser open for manual use after completion

## 🔧 Configuration

### Your Chrome Settings
- **Executable**: `C:\Program Files\Google\Chrome\Application\chrome.exe`
- **Profile**: `Profile 2`
- **User Data**: `C:\Users\<USER>\AppData\Local\Google\Chrome\User Data`

### Security Features
- ✅ Domain restrictions (only allowed websites)
- ✅ Session state preservation
- ✅ Video recording for debugging
- ✅ Error handling and recovery

### Allowed Domains
- `*.google.com`
- `*.duckduckgo.com`
- `*.github.com`
- `*.stackoverflow.com`
- `*.wikipedia.org`
- `localhost`
- `127.0.0.1`

## 🎮 Example Tasks

Edit the `tasks` list in `chrome_automation.py`:

```python
tasks = [
    "Go to DuckDuckGo, search for 'browser-use python automation', and summarize the first 3 results",
    "Go to GitHub, search for 'browser automation python', and find the most popular repositories",
    "Search Google for 'AI automation tools 2024' and create a summary of the top trends",
    "Go to Wikipedia, search for 'artificial intelligence', and summarize the main applications section",
    "Visit https://news.ycombinator.com and summarize the top 5 stories",
]
```

## 🛠️ Troubleshooting

### Common Issues

1. **"GEMINI_API_KEY not set"**
   - Edit `.env` file and add your API key

2. **"Chrome not found"**
   - Update `executable_path` in `chrome_automation.py`

3. **"User data directory not found"**
   - Update `user_data_dir` in `chrome_automation.py`

4. **Permission errors**
   - Run Command Prompt as Administrator

### Check Chrome Paths
```python
# Find your Chrome installation
import os
from pathlib import Path

# Common Chrome locations on Windows
chrome_paths = [
    r'C:\Program Files\Google\Chrome\Application\chrome.exe',
    r'C:\Program Files (x86)\Google\Chrome\Application\chrome.exe',
    r'C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe'
]

for path in chrome_paths:
    if Path(path).exists():
        print(f"Found Chrome: {path}")
```

## 🔒 Security Notes

- ✅ Only connects to your existing browser profile
- ✅ Preserves all authentication and settings
- ✅ Domain restrictions prevent unauthorized access
- ✅ Session state is saved locally
- ✅ No data sent to external services (except Gemini API for AI)

## 📊 Monitoring

- Check `recordings/` folder for session videos
- Browser stays open after automation for manual inspection
- All errors are logged to console

## 🎉 Success!

Once running, you'll see:
- Browser opens with your existing profile
- AI takes control and executes tasks
- Real-time progress in the console
- Browser remains open for manual use

Enjoy your AI-powered browser automation! 🚀
