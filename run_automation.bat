@echo off
echo 🚀 Browser-use Automation with Gemini
echo =====================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.11+ from https://python.org
    pause
    exit /b 1
)

REM Check if .env file exists and has API key
if not exist ".env" (
    echo ❌ .env file not found
    echo Please make sure the .env file exists
    pause
    exit /b 1
)

REM Check for API key in .env
findstr /C:"your_gemini_api_key_here" .env >nul
if not errorlevel 1 (
    echo ⚠️  Please update your Gemini API key in the .env file
    echo 🔗 Get your free key at: https://aistudio.google.com/app/u/1/apikey
    echo.
    echo Opening .env file for editing...
    notepad .env
    echo.
    echo After adding your API key, press any key to continue...
    pause >nul
)

REM Run the automation
echo 🤖 Starting browser automation...
python chrome_automation.py

echo.
echo 🎉 Automation completed!
pause
