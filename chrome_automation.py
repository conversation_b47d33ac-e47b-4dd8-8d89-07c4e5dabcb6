"""
Browser-use automation script with Gemini LLM
Connects to your existing Chrome Profile 2 and preserves all login sessions
"""

import asyncio
import os
import sys
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Import browser-use components
from browser_use import Agent, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>

def check_requirements():
    """Check if all requirements are met"""
    print("🔍 Checking requirements...")
    
    # Check if Gemini API key is set
    if not os.getenv('GEMINI_API_KEY') or os.getenv('GEMINI_API_KEY') == 'your_gemini_api_key_here':
        print("❌ GEMINI_API_KEY not set!")
        print("📝 Please edit the .env file and add your Gemini API key")
        print("🔗 Get your free key at: https://aistudio.google.com/app/u/1/apikey")
        return False
    
    # Check if Chrome executable exists
    chrome_path = r'C:\Program Files\Google\Chrome\Application\chrome.exe'
    if not Path(chrome_path).exists():
        print(f"❌ Chrome not found at: {chrome_path}")
        print("📝 Please update the executable_path in the script")
        return False
    
    # Check if user data directory exists
    user_data_dir = r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data'
    if not Path(user_data_dir).exists():
        print(f"❌ Chrome user data directory not found: {user_data_dir}")
        print("📝 Please update the user_data_dir in the script")
        return False
    
    print("✅ All requirements met!")
    return True

async def create_browser_session():
    """Create and configure the browser session"""
    print("🌐 Setting up browser connection...")
    
    browser = Browser(
        # Your specific Chrome configuration
        executable_path=r'C:\Program Files\Google\Chrome\Application\chrome.exe',
        user_data_dir=r'C:\Users\<USER>\AppData\Local\Google\Chrome\User Data',
        profile_directory='Profile 2',  # Your specific profile
        
        # Browser behavior
        headless=False,  # Keep visible to see what's happening
        keep_alive=True,  # Don't close browser after task
        
        # Security: Restrict to specific domains
        allowed_domains=[
            '*.google.com',
            '*.duckduckgo.com', 
            '*.github.com',
            '*.stackoverflow.com',
            '*.wikipedia.org',
            'localhost',
            '127.0.0.1'
        ],
        
        # Session persistence
        storage_state='./browser_session_state.json',
        
        # Performance settings
        record_video_dir='./recordings',  # Record sessions for debugging
        
        # Browser permissions
        permissions=['clipboardReadWrite', 'notifications'],
    )
    
    print("✅ Browser session configured!")
    return browser

async def run_automation_task(browser, task_description):
    """Run an automation task with the browser"""
    print(f"🤖 Starting automation task: {task_description}")
    
    # Create Gemini LLM instance
    llm = ChatGoogle(
        model="gemini-2.0-flash-exp",  # Latest Gemini model
        temperature=0.1,  # Low temperature for more consistent results
    )
    
    # Create agent
    agent = Agent(
        task=task_description,
        browser=browser,
        llm=llm,
        max_steps=20,  # Limit steps to prevent infinite loops
    )
    
    try:
        print("🚀 Running automation...")
        result = await agent.run()
        print("✅ Task completed successfully!")
        return result
    except Exception as e:
        print(f"❌ Error during automation: {e}")
        return None

async def main():
    """Main function"""
    print("🎯 Browser-use Automation with Gemini")
    print("=" * 50)
    
    # Check requirements
    if not check_requirements():
        return
    
    # Create browser session
    browser = await create_browser_session()
    
    # Example tasks - uncomment the one you want to run
    tasks = [
        # Basic web search
        "Go to DuckDuckGo, search for 'browser-use python automation', and summarize the first 3 results",
        
        # GitHub exploration
        # "Go to GitHub, search for 'browser automation python', and find the most popular repositories",
        
        # Google search with analysis
        # "Search Google for 'AI automation tools 2024' and create a summary of the top trends",
        
        # Wikipedia research
        # "Go to Wikipedia, search for 'artificial intelligence', and summarize the main applications section",
        
        # Custom task - modify as needed
        # "Visit https://news.ycombinator.com and summarize the top 5 stories",
    ]
    
    # Run the first task (you can change the index or modify the task)
    selected_task = tasks[0]
    
    try:
        result = await run_automation_task(browser, selected_task)
        
        if result:
            print("\n📋 Task Summary:")
            print("-" * 30)
            print("Task completed successfully!")
            print("Browser session is still active for manual use.")
            
        print("\n🔧 Next Steps:")
        print("1. Modify the task in the 'tasks' list above")
        print("2. Add your own custom tasks")
        print("3. Check the recordings folder for session videos")
        print("4. Browser will stay open for manual use (keep_alive=True)")
        
    except KeyboardInterrupt:
        print("\n⏹️ Automation stopped by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
    
    print("\n🎉 Automation session complete!")

if __name__ == '__main__':
    # Run the automation
    asyncio.run(main())
